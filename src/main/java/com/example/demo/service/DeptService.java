package com.example.demo.service;

import com.example.demo.mapper.DeptMapper;
import com.example.demo.pojo.Dept;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门业务逻辑层
 */
@Service
public class DeptService {

    @Autowired
    private DeptMapper deptMapper;

    /**
     * 查询所有部门
     */
    public List<Dept> list() {
        return deptMapper.list();
    }

    /**
     * 根据ID删除部门
     */
    public void delete(Integer id) {
        deptMapper.deleteById(id);
    }

    /**
     * 新增部门
     */
    public void add(Dept dept) {
        dept.setCreateTime(LocalDateTime.now());
        dept.setUpdateTime(LocalDateTime.now());
        deptMapper.insert(dept);
    }

    /**
     * 根据ID查询部门
     */
    public Dept getById(Integer id) {
        return deptMapper.getById(id);
    }

    /**
     * 更新部门
     */
    public void update(Dept dept) {
        dept.setUpdateTime(LocalDateTime.now());
        deptMapper.update(dept);
    }
}
