// 全局变量
let currentEditId = null;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadDeptList();
});

// 加载部门列表
async function loadDeptList() {
    try {
        const response = await fetch('/depts');
        const result = await response.json();
        
        if (result.code === 1) {
            renderDeptTable(result.data);
        } else {
            alert('加载部门列表失败: ' + result.msg);
        }
    } catch (error) {
        console.error('加载部门列表出错:', error);
        alert('加载部门列表出错，请检查网络连接');
    }
}

// 渲染部门表格
function renderDeptTable(deptList) {
    const tbody = document.getElementById('deptTableBody');
    tbody.innerHTML = '';
    
    if (!deptList || deptList.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #999;">暂无数据</td></tr>';
        return;
    }
    
    deptList.forEach((dept, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${dept.name}</td>
            <td>${formatDateTime(dept.updateTime)}</td>
            <td>
                <button class="btn-edit" onclick="editDept(${dept.id})">编辑</button>
                <button class="btn-delete" onclick="deleteDept(${dept.id})">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    return date.getFullYear() + '-' + 
           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
           String(date.getDate()).padStart(2, '0') + ' ' +
           String(date.getHours()).padStart(2, '0') + ':' +
           String(date.getMinutes()).padStart(2, '0') + ':' +
           String(date.getSeconds()).padStart(2, '0');
}

// 显示新增模态框
function showAddModal() {
    currentEditId = null;
    document.getElementById('modalTitle').textContent = '新增部门';
    document.getElementById('deptForm').reset();
    document.getElementById('deptId').value = '';
    document.getElementById('deptModal').style.display = 'block';
}

// 编辑部门
async function editDept(id) {
    try {
        const response = await fetch(`/depts/${id}`);
        const result = await response.json();
        
        if (result.code === 1) {
            currentEditId = id;
            document.getElementById('modalTitle').textContent = '编辑部门';
            document.getElementById('deptId').value = id;
            document.getElementById('deptName').value = result.data.name;
            document.getElementById('deptModal').style.display = 'block';
        } else {
            alert('获取部门信息失败: ' + result.msg);
        }
    } catch (error) {
        console.error('获取部门信息出错:', error);
        alert('获取部门信息出错');
    }
}

// 删除部门
async function deleteDept(id) {
    if (!confirm('确定要删除这个部门吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/depts/${id}`, {
            method: 'DELETE'
        });
        const result = await response.json();
        
        if (result.code === 1) {
            alert('删除成功');
            loadDeptList(); // 重新加载列表
        } else {
            alert('删除失败: ' + result.msg);
        }
    } catch (error) {
        console.error('删除部门出错:', error);
        alert('删除部门出错');
    }
}

// 保存部门
async function saveDept() {
    const name = document.getElementById('deptName').value.trim();
    
    if (!name) {
        alert('请输入部门名称');
        return;
    }
    
    const deptData = { name: name };
    
    try {
        let response;
        if (currentEditId) {
            // 编辑
            deptData.id = currentEditId;
            response = await fetch('/depts', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(deptData)
            });
        } else {
            // 新增
            response = await fetch('/depts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(deptData)
            });
        }
        
        const result = await response.json();
        
        if (result.code === 1) {
            alert(currentEditId ? '更新成功' : '新增成功');
            closeModal();
            loadDeptList(); // 重新加载列表
        } else {
            alert('保存失败: ' + result.msg);
        }
    } catch (error) {
        console.error('保存部门出错:', error);
        alert('保存部门出错');
    }
}

// 关闭模态框
function closeModal() {
    document.getElementById('deptModal').style.display = 'none';
    currentEditId = null;
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('deptModal');
    if (event.target === modal) {
        closeModal();
    }
}
