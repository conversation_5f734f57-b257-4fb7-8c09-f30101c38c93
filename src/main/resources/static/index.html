<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TLIAS智能学习辅助系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-left">
            <i class="fas fa-graduation-cap"></i>
            <span>TLIAS智能学习辅助系统</span>
        </div>
        <div class="header-center">
            <nav class="nav-tabs">
                <a href="#" class="nav-tab">Dashboard</a>
                <a href="#" class="nav-tab">系统信息管理</a>
                <a href="#" class="nav-tab active">部门管理</a>
            </nav>
        </div>
        <div class="header-right">
            <span class="user-info">管理员</span>
            <i class="fas fa-user-circle"></i>
        </div>
    </header>

    <!-- 主体内容 -->
    <div class="container">
        <!-- 左侧导航 -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>机构学院管理</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-users"></i>
                    <span>部门信息管理</span>
                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="content-header">
                <h2>部门信息管理</h2>
                <button class="btn-primary" onclick="showAddModal()">
                    <i class="fas fa-plus"></i>
                    新增部门
                </button>
            </div>

            <!-- 部门列表表格 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>部门名称</th>
                            <th>最近操作时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="deptTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- 新增/编辑部门模态框 -->
    <div id="deptModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增部门</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="deptForm">
                    <input type="hidden" id="deptId">
                    <div class="form-group">
                        <label for="deptName">部门名称:</label>
                        <input type="text" id="deptName" name="name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn-primary" onclick="saveDept()">保存</button>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
