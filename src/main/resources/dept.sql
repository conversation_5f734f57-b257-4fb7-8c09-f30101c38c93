-- 部门表创建脚本
CREATE DATABASE IF NOT EXISTS mybatis;
USE mybatis;

-- 创建部门表
CREATE TABLE IF NOT EXISTS dept (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '部门ID',
    name VARCHAR(50) NOT NULL COMMENT '部门名称',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间'
) COMMENT '部门表';

-- 插入测试数据
INSERT INTO dept (name, create_time, update_time) VALUES 
('研发部', NOW(), NOW()),
('市场部', NOW(), NOW()),
('财务部', NOW(), NOW()),
('人事部', NOW(), NOW());
