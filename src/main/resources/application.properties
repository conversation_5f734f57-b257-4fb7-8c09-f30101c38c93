# ?????
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=****************************************************************************************************
spring.datasource.username=root
spring.datasource.password=1234

# MyBatis??
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis.configuration.map-underscore-to-camel-case=true

# ?????
server.port=8080

# ??????
spring.web.resources.static-locations=classpath:/static/

# ????
logging.level.com.example.demo=DEBUG